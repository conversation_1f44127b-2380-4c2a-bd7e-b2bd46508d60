import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { authService } from '@/services/authService';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const AdminDiagnostic: React.FC = () => {
  const { user, loading, isAdmin, signIn } = useAuth();
  const [diagnostics, setDiagnostics] = useState<any>({});
  const [testResults, setTestResults] = useState<string[]>([]);

  useEffect(() => {
    runDiagnostics();
  }, [user]);

  const runDiagnostics = async () => {
    const results: string[] = [];
    const diag: any = {};

    // Check if user is logged in
    diag.userLoggedIn = !!user;
    results.push(`User logged in: ${diag.userLoggedIn ? 'YES' : 'NO'}`);

    if (user) {
      diag.userEmail = user.email;
      diag.userRole = user.role;
      diag.isAdmin = isAdmin;
      results.push(`User email: ${user.email}`);
      results.push(`User role: ${user.role}`);
      results.push(`Is admin: ${isAdmin ? 'YES' : 'NO'}`);
    }

    // Check token in localStorage
    const accessToken = localStorage.getItem('accessToken');
    diag.hasAccessToken = !!accessToken;
    results.push(`Has access token: ${diag.hasAccessToken ? 'YES' : 'NO'}`);

    // Check if token is valid
    if (accessToken) {
      try {
        const currentUser = authService.getCurrentUser();
        diag.tokenValid = !!currentUser;
        results.push(`Token valid: ${diag.tokenValid ? 'YES' : 'NO'}`);
        if (currentUser) {
          results.push(`Token user role: ${currentUser.role}`);
        }
      } catch (error) {
        diag.tokenValid = false;
        results.push(`Token validation error: ${error}`);
      }
    }

    // Test API connection
    try {
      const response = await fetch('http://localhost:3001/api/v1/auth/me', {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      });
      diag.apiConnected = response.ok;
      results.push(`API connection: ${response.ok ? 'SUCCESS' : 'FAILED'}`);
      
      if (response.ok) {
        const data = await response.json();
        results.push(`API user role: ${data.data?.role}`);
      }
    } catch (error) {
      diag.apiConnected = false;
      results.push(`API connection error: ${error}`);
    }

    setDiagnostics(diag);
    setTestResults(results);
  };

  const testAdminLogin = async () => {
    try {
      const result = await signIn('<EMAIL>', 'Admin123!@#');
      if (result.error) {
        setTestResults(prev => [...prev, `Login failed: ${result.error.message}`]);
      } else {
        setTestResults(prev => [...prev, 'Login successful!']);
        setTimeout(() => runDiagnostics(), 1000);
      }
    } catch (error: any) {
      setTestResults(prev => [...prev, `Login error: ${error.message}`]);
    }
  };

  const goToAdmin = () => {
    window.location.href = '/admin';
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Admin Dashboard Diagnostic</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold mb-2">Current Status</h3>
                <div className="bg-gray-50 p-3 rounded text-sm">
                  {testResults.map((result, index) => (
                    <div key={index} className="mb-1">{result}</div>
                  ))}
                </div>
              </div>
              
              <div>
                <h3 className="font-semibold mb-2">Actions</h3>
                <div className="space-y-2">
                  <Button onClick={testAdminLogin} className="w-full">
                    Test Admin Login
                  </Button>
                  <Button onClick={runDiagnostics} variant="outline" className="w-full">
                    Refresh Diagnostics
                  </Button>
                  {isAdmin && (
                    <Button onClick={goToAdmin} variant="default" className="w-full bg-green-600 hover:bg-green-700">
                      Go to Admin Dashboard
                    </Button>
                  )}
                </div>
              </div>
            </div>

            {user && (
              <div className="mt-6 p-4 bg-blue-50 rounded">
                <h3 className="font-semibold text-blue-800 mb-2">User Information</h3>
                <pre className="text-sm text-blue-700">
                  {JSON.stringify(user, null, 2)}
                </pre>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminDiagnostic;
