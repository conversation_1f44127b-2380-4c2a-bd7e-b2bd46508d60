import { useQuery } from '@tanstack/react-query';
import { MockDataService, MockProperty } from '@/services/mockDataService';

export const useProperty = (id: string) => {
  return useQuery({
    queryKey: ['property', id],
    queryFn: async (): Promise<MockProperty | null> => {
      return await MockDataService.getProperty(id);
    },
    enabled: !!id,
  });
};

export const useProperties = () => {
  return useQuery({
    queryKey: ['properties'],
    queryFn: async (): Promise<MockProperty[]> => {
      return await MockDataService.getProperties();
    },
  });
};
