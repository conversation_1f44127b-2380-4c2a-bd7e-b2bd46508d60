import { useQuery } from '@tanstack/react-query';
import { MockDataService, MockProperty } from '@/services/mockDataService';
import { sampleProperties } from '@/components/admin/seed/SampleData';

type Property = MockProperty;

export const useProperty = (id: string) => {
  return useQuery({
    queryKey: ['property', id],
    queryFn: async () => {
      // Use sample data in development mode for immediate preview
      const isDevelopment = import.meta.env.DEV;

      if (isDevelopment) {
        // Find property in sample data by ID
        await new Promise(resolve => setTimeout(resolve, 300)); // Simulate loading
        const property = sampleProperties.find(p => p.id === id);

        if (!property) {
          throw new Error('Property not found');
        }

        return property;
      }

      // Use mock data service instead of Supabase
      const property = await MockDataService.getProperty(id);

      if (!property) {
        throw new Error('Property not found');
      }

      return property;
    },
    enabled: !!id,
  });
};
