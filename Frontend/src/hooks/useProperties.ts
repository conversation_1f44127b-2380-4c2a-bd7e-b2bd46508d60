
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { sampleProperties } from '@/components/admin/seed/SampleData';
import { API_CONFIG } from '@/config/api';

// Define Property types based on our backend API
interface Property {
  id: string;
  title: string;
  description: string;
  location: string;
  price: number;
  property_type: string;
  bedrooms: number;
  bathrooms: number;
  area_sqft: number;
  is_available: boolean;
  is_verified: boolean;
  is_featured: boolean;
  images: string[];
  created_at: string;
  updated_at: string;
}

interface PropertyInsert {
  title: string;
  description: string;
  location: string;
  price: number;
  property_type: string;
  bedrooms: number;
  bathrooms: number;
  area_sqft: number;
  is_available?: boolean;
  is_verified?: boolean;
  is_featured?: boolean;
  images?: string[];
}

export const useProperties = (filters?: {
  search?: string;
  location?: string;
  propertyType?: string;
  priceRange?: string;
  minPrice?: number;
  maxPrice?: number;
  bedrooms?: string;
  bathrooms?: string;
  isVerified?: boolean;
  isFeatured?: boolean;
}) => {
  return useQuery({
    queryKey: ['properties', filters],
    queryFn: async () => {
      // Use sample data in development mode for immediate preview
      const isDevelopment = import.meta.env.DEV;

      if (isDevelopment) {
        // Return sample data with simulated delay
        await new Promise(resolve => setTimeout(resolve, 500));
        return sampleProperties.filter(property => property.is_available);
      }

      // Build query parameters for backend API
      const queryParams = new URLSearchParams();
      queryParams.append('is_available', 'true');

      if (filters?.search) {
        queryParams.append('search', filters.search);
      }

      if (filters?.location && filters.location !== 'all') {
        queryParams.append('location', filters.location);
      }

      if (filters?.propertyType && filters.propertyType !== 'all') {
        queryParams.append('property_type', filters.propertyType);
      }

      if (filters?.bedrooms && filters.bedrooms !== 'all') {
        queryParams.append('bedrooms', filters.bedrooms);
      }

      if (filters?.bathrooms && filters.bathrooms !== 'all') {
        queryParams.append('bathrooms', filters.bathrooms);
      }

      if (filters?.minPrice) {
        queryParams.append('min_price', filters.minPrice.toString());
      }

      if (filters?.maxPrice) {
        queryParams.append('max_price', filters.maxPrice.toString());
      }

      if (filters?.isVerified !== undefined) {
        queryParams.append('is_verified', filters.isVerified.toString());
      }

      if (filters?.isFeatured !== undefined) {
        queryParams.append('is_featured', filters.isFeatured.toString());
      }

      if (filters?.priceRange && filters.priceRange !== 'all') {
        const [min, max] = filters.priceRange.split('-').map(p => parseInt(p));
        queryParams.append('min_price', min.toString());
        if (max) {
          queryParams.append('max_price', max.toString());
        }
      }

      const response = await fetch(`${API_CONFIG.BASE_URL}/properties?${queryParams.toString()}`, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch properties: ${response.statusText}`);
      }

      const result = await response.json();
      return result.data || [];
    },
  });
};

export const useCreateProperty = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (property: PropertyInsert) => {
      const response = await fetch(`${API_CONFIG.BASE_URL}/properties`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
        },
        body: JSON.stringify(property),
      });

      if (!response.ok) {
        throw new Error(`Failed to create property: ${response.statusText}`);
      }

      const result = await response.json();
      return result.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['properties'] });
    },
  });
};

export const useUpdateProperty = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<Property> }) => {
      const response = await fetch(`${API_CONFIG.BASE_URL}/properties/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        throw new Error(`Failed to update property: ${response.statusText}`);
      }

      const result = await response.json();
      return result.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['properties'] });
    },
  });
};

export const usePropertyInquiry = () => {
  return useMutation({
    mutationFn: async (inquiry: {
      property_id: string;
      inquirer_name: string;
      inquirer_email: string;
      inquirer_phone?: string;
      message?: string;
      inquiry_type?: string;
    }) => {
      const response = await fetch(`${API_CONFIG.BASE_URL}/property-inquiries`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
        },
        body: JSON.stringify(inquiry),
      });

      if (!response.ok) {
        throw new Error(`Failed to create inquiry: ${response.statusText}`);
      }

      const result = await response.json();
      return result.data;
    },
  });
};
