
import React from 'react';
import { useAuth } from '@/hooks/useAuth';
import { Navigate, useNavigate, useLocation } from 'react-router-dom';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string | string[];
  requireAdmin?: boolean;
  fallback?: React.ReactNode;
  redirectTo?: string;
  skipEmailVerification?: boolean;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
  requireAdmin = false,
  fallback,
  redirectTo = '/auth',
  skipEmailVerification = false
}) => {
  const { user, loading, isAdmin } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  // Check if user is authenticated
  if (!user) {
    if (fallback) {
      return <>{fallback}</>;
    }

    // Redirect to auth page with return URL
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  // Check if user account is active
  if (!user.isActive) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">Account Deactivated</h2>
          <p className="text-gray-600 mb-4">
            Your account has been deactivated. Please contact support for assistance.
          </p>
          <button
            onClick={() => navigate('/contact')}
            className="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
          >
            Contact Support
          </button>
        </div>
      </div>
    );
  }

  // Check if email is verified (optional requirement)
  if (!skipEmailVerification && !user.isEmailVerified) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center max-w-md mx-auto p-6">
          <h2 className="text-2xl font-bold text-orange-600 mb-4">Email Verification Required</h2>
          <p className="text-gray-600 mb-4">
            Please check your email and click the verification link to activate your account.
          </p>
          <div className="space-y-2">
            <button
              onClick={() => navigate('/resend-verification')}
              className="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors mr-2"
            >
              Resend Verification
            </button>
            <button
              onClick={() => navigate('/')}
              className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
            >
              Go Home
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Check admin requirement
  if (requireAdmin && !isAdmin) {
    if (fallback) {
      return <>{fallback}</>;
    }

    // Redirect to appropriate dashboard based on user role
    const roleRedirects: Record<string, string> = {
      'agent': '/enhanced-agent-dashboard',
      'landlord': '/landlord-portal',
      'tenant': '/tenant-portal',
    };

    const redirectPath = roleRedirects[user.role.toLowerCase()] || '/tenant-portal';
    return <Navigate to={redirectPath} replace />;
  }

  // Check specific role requirement
  if (requiredRole) {
    const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
    const userRole = user.role.toLowerCase();
    const hasRequiredRole = roles.some(role => role.toLowerCase() === userRole);

    if (!hasRequiredRole) {
      if (fallback) {
        return <>{fallback}</>;
      }

      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h2>
            <p className="text-gray-600 mb-4">
              You don't have permission to access this page.
            </p>
            <button
              onClick={() => navigate('/')}
              className="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
            >
              Go Home
            </button>
          </div>
        </div>
      );
    }
  }

  // User is authenticated and authorized
  return <>{children}</>;
};

export default ProtectedRoute;

// Convenience components for specific roles
export const AdminRoute: React.FC<Omit<ProtectedRouteProps, 'requireAdmin'>> = (props) => (
  <ProtectedRoute {...props} requireAdmin={true} />
);

export const AgentRoute: React.FC<Omit<ProtectedRouteProps, 'requiredRole'>> = (props) => (
  <ProtectedRoute {...props} requiredRole="agent" />
);

export const LandlordRoute: React.FC<Omit<ProtectedRouteProps, 'requiredRole'>> = (props) => (
  <ProtectedRoute {...props} requiredRole="landlord" />
);

export const TenantRoute: React.FC<Omit<ProtectedRouteProps, 'requiredRole'>> = (props) => (
  <ProtectedRoute {...props} requiredRole="tenant" />
);

// Multi-role route
export const MultiRoleRoute: React.FC<Omit<ProtectedRouteProps, 'requiredRole'> & { roles: string[] }> = ({ roles, ...props }) => (
  <ProtectedRoute {...props} requiredRole={roles} />
);
