import { jwtDecode } from 'jwt-decode';
import { API_CONFIG, AUTH_ENDPOINTS, HTTP_STATUS, ERROR_MESSAGES } from '@/config/api';

// Types matching the backend DTOs and interfaces
export interface LoginDto {
  email: string;
  password: string;
}

export interface RegisterDto {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone: string;
  role?: 'tenant' | 'landlord' | 'agent' | 'admin';
}

export interface AuthResponse {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: string;
    isEmailVerified: boolean;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  };
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: string;
}

export interface RefreshTokenDto {
  refreshToken: string;
}

export interface ChangePasswordDto {
  currentPassword: string;
  newPassword: string;
}

export interface ForgotPasswordDto {
  email: string;
}

export interface ResetPasswordDto {
  token: string;
  newPassword: string;
}

export interface VerifyEmailDto {
  token: string;
}

export interface JwtPayload {
  sub: string;
  email: string;
  role: string;
  iat: number;
  exp: number;
}

class AuthService {
  private accessToken: string | null = null;
  private refreshToken: string | null = null;
  private tokenExpiry: number | null = null;
  private refreshPromise: Promise<AuthResponse> | null = null;

  constructor() {
    // Load tokens from localStorage on initialization
    this.loadTokensFromStorage();
  }

  // Token Management
  private loadTokensFromStorage(): void {
    this.accessToken = localStorage.getItem('accessToken');
    this.refreshToken = localStorage.getItem('refreshToken');
    const expiry = localStorage.getItem('tokenExpiry');
    this.tokenExpiry = expiry ? parseInt(expiry) : null;
  }

  private saveTokensToStorage(accessToken: string, refreshToken: string, expiresIn: number): void {
    this.accessToken = accessToken;
    this.refreshToken = refreshToken;
    this.tokenExpiry = Date.now() + (expiresIn * 1000);
    
    localStorage.setItem('accessToken', accessToken);
    localStorage.setItem('refreshToken', refreshToken);
    localStorage.setItem('tokenExpiry', this.tokenExpiry.toString());
  }

  private clearTokensFromStorage(): void {
    this.accessToken = null;
    this.refreshToken = null;
    this.tokenExpiry = null;
    
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('tokenExpiry');
  }

  private isTokenExpired(): boolean {
    if (!this.tokenExpiry) return true;
    // Add 30 second buffer to prevent edge cases
    return Date.now() >= (this.tokenExpiry - 30000);
  }

  private isTokenValid(token: string): boolean {
    if (!token) return false;

    try {
      const decoded = jwtDecode<JwtPayload>(token);
      const currentTime = Date.now() / 1000;

      // Check if token is expired
      if (decoded.exp && decoded.exp < currentTime) {
        return false;
      }

      // Check if token is issued in the future (clock skew protection)
      if (decoded.iat && decoded.iat > currentTime + 300) { // 5 minute tolerance
        return false;
      }

      return true;
    } catch (error) {
      console.error('Token validation failed:', error);
      return false;
    }
  }

  private async getAuthHeaders(): Promise<HeadersInit> {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (this.accessToken && !this.isTokenExpired()) {
      headers.Authorization = `Bearer ${this.accessToken}`;
    }

    return headers;
  }

  // HTTP Client with retry logic
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {},
    retryCount = 0
  ): Promise<T> {
    const url = `${API_CONFIG.BASE_URL}${endpoint}`;
    const headers = await this.getAuthHeaders();

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.TIMEOUT);

    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          ...headers,
          ...options.headers,
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));

        // Handle token refresh on 401 errors
        if (response.status === HTTP_STATUS.UNAUTHORIZED && retryCount < API_CONFIG.RETRY_ATTEMPTS) {
          try {
            await this.refreshAccessToken();
            return this.makeRequest(endpoint, options, retryCount + 1);
          } catch (refreshError) {
            // Report refresh error
            const { errorHandler } = await import('./errorHandler');
            errorHandler.handleApiError(refreshError, {
              component: 'AuthService',
              action: 'Token Refresh',
            });
            // If refresh fails, clear tokens and throw original error
            this.clearTokensFromStorage();
          }
        }
        
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error(ERROR_MESSAGES.TIMEOUT_ERROR);
        }
        throw error;
      }
      
      throw new Error(ERROR_MESSAGES.NETWORK_ERROR);
    }
  }

  // Auth Methods
  async register(registerDto: RegisterDto): Promise<AuthResponse> {
    const authResponse = await this.makeRequest<any>(AUTH_ENDPOINTS.REGISTER, {
      method: 'POST',
      body: JSON.stringify(registerDto),
    });
    const response: AuthResponse = authResponse.data;
    this.saveTokensToStorage(response.accessToken, response.refreshToken, response.expiresIn);
    return response;
  }

  async login(loginDto: LoginDto): Promise<AuthResponse> {
    const authResponse = await this.makeRequest<any>(AUTH_ENDPOINTS.LOGIN, {
      method: 'POST',
      body: JSON.stringify(loginDto),
    });
    const response: AuthResponse = authResponse.data;
    this.saveTokensToStorage(response.accessToken, response.refreshToken, response.expiresIn);
    return response;
  }

  async refreshAccessToken(): Promise<AuthResponse> {
    if (!this.refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await this.makeRequest<AuthResponse>(AUTH_ENDPOINTS.REFRESH, {
      method: 'POST',
      body: JSON.stringify({ refreshToken: this.refreshToken }),
    });

    this.saveTokensToStorage(response.accessToken, response.refreshToken, response.expiresIn);
    return response;
  }

  async logout(): Promise<void> {
    if (this.accessToken) {
      try {
        await this.makeRequest(AUTH_ENDPOINTS.LOGOUT, {
          method: 'POST',
          body: JSON.stringify({ refreshToken: this.refreshToken }),
        });
      } catch (error) {
        // Continue with logout even if API call fails
        console.warn('Logout API call failed:', error);
      }
    }

    this.clearTokensFromStorage();
  }

  async changePassword(changePasswordDto: ChangePasswordDto): Promise<void> {
    await this.makeRequest(AUTH_ENDPOINTS.CHANGE_PASSWORD, {
      method: 'PATCH',
      body: JSON.stringify(changePasswordDto),
    });
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<void> {
    await this.makeRequest(AUTH_ENDPOINTS.FORGOT_PASSWORD, {
      method: 'POST',
      body: JSON.stringify(forgotPasswordDto),
    });
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<void> {
    await this.makeRequest(AUTH_ENDPOINTS.RESET_PASSWORD, {
      method: 'POST',
      body: JSON.stringify(resetPasswordDto),
    });
  }

  async verifyEmail(verifyEmailDto: VerifyEmailDto): Promise<void> {
    await this.makeRequest(AUTH_ENDPOINTS.VERIFY_EMAIL, {
      method: 'POST',
      body: JSON.stringify(verifyEmailDto),
    });
  }

  async getProfile(): Promise<any> {
    return await this.makeRequest(AUTH_ENDPOINTS.GET_PROFILE, {
      method: 'GET',
    });
  }

  // Utility Methods
  getCurrentUser(): any {
    if (!this.accessToken) return null;

    try {
      const decoded = jwtDecode<JwtPayload>(this.accessToken);
      return {
        id: decoded.sub,
        email: decoded.email,
        role: decoded.role,
      };
    } catch (error) {
      console.error('Failed to decode token:', error);
      return null;
    }
  }

  isAuthenticated(): boolean {
    return !!(this.accessToken && !this.isTokenExpired());
  }

  async ensureValidToken(): Promise<boolean> {
    if (!this.accessToken) return false;

    if (this.isTokenExpired()) {
      // Explicit refreshToken check
      if (!this.refreshToken) {
        this.clearTokensFromStorage();
        return false;
      }
      
      try {
        await this.refreshAccessToken();
        return true;
      } catch (error) {
        this.clearTokensFromStorage();
        return false;
      }
    }

    return true;
  }

  getAccessToken(): string | null {
    return this.accessToken;
  }

  // Enhanced token validation
  validateToken(token: string): boolean {
    if (!token) return false;

    try {
      const decoded = jwtDecode<JwtPayload>(token);
      const currentTime = Date.now() / 1000;

      // Check if token is expired
      if (decoded.exp && decoded.exp < currentTime) {
        return false;
      }

      // Check if token is issued in the future (clock skew protection)
      if (decoded.iat && decoded.iat > currentTime + 300) { // 5 minute tolerance
        return false;
      }

      // Check required fields
      if (!decoded.sub || !decoded.email || !decoded.role) {
        return false;
      }

      return true;
    } catch (error) {
      console.error('Token validation failed:', error);
      return false;
    }
  }

  // Get token expiry time
  getTokenExpiry(): number | null {
    return this.tokenExpiry;
  }

  // Check if token will expire soon (within 5 minutes)
  isTokenExpiringSoon(): boolean {
    if (!this.tokenExpiry) return true;
    return Date.now() >= (this.tokenExpiry - 300000); // 5 minutes
  }
}

// Export singleton instance
export const authService = new AuthService();