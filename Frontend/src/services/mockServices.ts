// Mock services to replace Supabase-dependent services
// This provides simplified implementations for development

import { MockDataService } from './mockDataService';

// Performance Monitoring Service Mock
export const performanceMonitoringService = {
  trackPageLoad: (page: string, loadTime: number) => {
    console.log(`Page load tracked: ${page} - ${loadTime}ms`);
  },
  trackUserAction: (action: string, metadata?: any) => {
    console.log(`User action tracked: ${action}`, metadata);
  },
  getMetrics: async () => {
    return {
      pageLoadTime: 1200,
      userActions: 45,
      errorRate: 0.02,
      uptime: 99.9
    };
  }
};

// Validation Service Mock
export const validationService = {
  validateEmail: (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },
  validatePhone: (phone: string) => {
    const phoneRegex = /^\+?[\d\s-()]+$/;
    return phoneRegex.test(phone);
  },
  validatePassword: (password: string) => {
    return password.length >= 8;
  },
  sanitizeInput: (input: string) => {
    return input.trim().replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  }
};

// Real-time Data Service Mock
export const realTimeDataService = {
  subscribe: (channel: string, callback: (data: any) => void) => {
    console.log(`Subscribed to channel: ${channel}`);
    // Simulate real-time updates
    const interval = setInterval(() => {
      callback({ type: 'update', data: { timestamp: Date.now() } });
    }, 5000);
    
    return () => clearInterval(interval);
  },
  publish: (channel: string, data: any) => {
    console.log(`Published to channel: ${channel}`, data);
  }
};

// Email Service Mock
export const emailService = {
  sendEmail: async (to: string, subject: string, body: string) => {
    console.log(`Email sent to: ${to}, Subject: ${subject}`);
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true, messageId: `msg_${Date.now()}` };
  },
  sendTemplate: async (to: string, templateId: string, variables: any) => {
    console.log(`Template email sent to: ${to}, Template: ${templateId}`, variables);
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true, messageId: `msg_${Date.now()}` };
  }
};

// SMS Service Mock
export const smsService = {
  sendSMS: async (to: string, message: string) => {
    console.log(`SMS sent to: ${to}, Message: ${message}`);
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true, messageId: `sms_${Date.now()}` };
  }
};

// WhatsApp Service Mock
export const whatsappService = {
  sendMessage: async (to: string, message: string) => {
    console.log(`WhatsApp message sent to: ${to}, Message: ${message}`);
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true, messageId: `wa_${Date.now()}` };
  },
  sendTemplate: async (to: string, templateName: string, parameters: any) => {
    console.log(`WhatsApp template sent to: ${to}, Template: ${templateName}`, parameters);
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true, messageId: `wa_${Date.now()}` };
  }
};

// Messaging Service Mock
export const messagingService = {
  sendMessage: async (userId: string, message: string, type: string = 'info') => {
    console.log(`Message sent to user: ${userId}, Type: ${type}, Message: ${message}`);
    await new Promise(resolve => setTimeout(resolve, 300));
    return { success: true, messageId: `msg_${Date.now()}` };
  },
  getMessages: async (userId: string) => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return [
      {
        id: '1',
        userId,
        message: 'Welcome to PHCityRent!',
        type: 'info',
        read: false,
        createdAt: new Date().toISOString()
      }
    ];
  }
};

// Dashboard Service Mock
export const dashboardService = {
  getStats: async () => {
    return await MockDataService.getStats();
  },
  getRecentActivity: async () => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return [
      {
        id: '1',
        type: 'property_viewed',
        description: 'Property viewed by user',
        timestamp: new Date().toISOString()
      }
    ];
  }
};

// Market Trend Analysis Service Mock
export const marketTrendAnalysisService = {
  getMarketTrends: async (location: string) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return {
      location,
      averagePrice: 250000,
      priceChange: 5.2,
      demandLevel: 'high',
      supplyLevel: 'medium',
      trends: [
        { month: 'Jan', price: 240000 },
        { month: 'Feb', price: 245000 },
        { month: 'Mar', price: 250000 }
      ]
    };
  },
  getPriceRecommendation: async (propertyData: any) => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return {
      recommendedPrice: 275000,
      confidence: 0.85,
      factors: ['location', 'size', 'amenities']
    };
  }
};

// Performance Metrics Service Mock
export const performanceMetricsService = {
  getMetrics: async () => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return {
      responseTime: 120,
      throughput: 1500,
      errorRate: 0.01,
      uptime: 99.95
    };
  },
  trackMetric: (name: string, value: number) => {
    console.log(`Metric tracked: ${name} = ${value}`);
  }
};

// Report Generation Service Mock
export const reportGenerationService = {
  generateReport: async (type: string, parameters: any) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    return {
      reportId: `report_${Date.now()}`,
      type,
      status: 'completed',
      downloadUrl: `/reports/report_${Date.now()}.pdf`
    };
  },
  getReportStatus: async (reportId: string) => {
    await new Promise(resolve => setTimeout(resolve, 200));
    return {
      reportId,
      status: 'completed',
      progress: 100
    };
  }
};

export default {
  performanceMonitoringService,
  validationService,
  realTimeDataService,
  emailService,
  smsService,
  whatsappService,
  messagingService,
  dashboardService,
  marketTrendAnalysisService,
  performanceMetricsService,
  reportGenerationService
};
